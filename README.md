# Music Manager - Spotify Downloader Setup

This repository contains my complete spotdl (Spotify downloader) setup for downloading music from Spotify to my Music folder.

## Quick Setup

### 1. Install Dependencies
```bash
# Install Python package
pip3 install spotdl

# Install FFmpeg (macOS with Homebrew)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
brew install ffmpeg
```

### 2. Install Scripts
```bash
# Create bin directory
mkdir -p ~/bin

# Copy scripts
cp bin/spotdl ~/bin/
cp bin/spotdl-batch ~/bin/
chmod +x ~/bin/spotdl ~/bin/spotdl-batch

# Add to PATH
echo 'export PATH="$HOME/bin:$PATH"' >> ~/.zshrc
echo 'export PATH="/opt/homebrew/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

### 3. Setup Configuration
```bash
# Create config directory
mkdir -p ~/.spotdl

# Copy config file
cp config/config.json ~/.spotdl/

# Copy links template to Music folder
cp config/spotify_links.txt ~/Music/
```

## Usage

### Download Single Song/Playlist
```bash
spotdl "https://open.spotify.com/track/your-link-here"
```

### Batch Download
1. Add Spotify links to `~/Music/spotify_links.txt` (one per line)
2. Run: `spotdl-batch`

## Features

✅ **Maximum Quality by Default** - FLAC Lossless audio (16-bit, 48kHz)
✅ **Smart folder organization** - Artist and Album folders automatically created
✅ **Primary artist organization** - Featured artists don't create separate folders
✅ **Complete music collection** - 27+ albums ready to download
✅ **Smart duplicate detection** - Skips songs you already have
✅ **Batch processing** - Download entire collection at once
✅ **Progress tracking** - Shows download progress
✅ **Error handling** - Continues if one song fails
✅ **Multiple quality options** - FLAC (default) or MP3 when needed

## Folder Organization

Your music will be automatically organized like this:

```
~/Music/
├── Kanye West/
│   ├── Stronger.mp3                    (single track)
│   └── Graduation/                     (album folder)
│       ├── 1 - Good Morning.mp3
│       ├── 2 - Champion.mp3
│       ├── 3 - Stronger.mp3
│       └── ...
├── Taylor Swift/
│   ├── Anti-Hero.mp3                   (single track)
│   └── Midnights/                      (album folder)
│       ├── 1 - Lavender Haze.mp3
│       ├── 2 - Maroon.mp3
│       └── ...
```

**Organization Rules:**
- 🎵 **Singles**: `~/Music/{Artist}/{Title}.flac`
- 💿 **Albums**: `~/Music/{Artist}/{Album}/{Track#} - {Title}.flac`
- 🎯 **Primary Artist Only**: Featured artists don't create separate folders

## Quick Start

1. **Download your complete music collection (27+ albums):**
   ```bash
   spotdl-batch
   ```

2. **Download a single song/album:**
   ```bash
   spotdl "https://open.spotify.com/track/4iV5W9uYEdYUVa79Axb7Rh"
   ```

3. **Download with MP3 (smaller files):**
   ```bash
   spotdl-mp3 "https://open.spotify.com/album/..."
   ```

4. **Force redownload everything:**
   ```bash
   spotdl-batch --force
   ```

## Quality Options

- **Default**: FLAC Lossless (~25-40MB per song, perfect quality)
- **Alternative**: MP3 320k (~8-12MB per song, high quality)

## Files

- `bin/spotdl` - Main download script (FLAC quality)
- `bin/spotdl-batch` - Batch download script with duplicate detection
- `bin/spotdl-mp3` - MP3 download script (smaller files)
- `bin/spotdl-slow` - Slow download mode (avoids rate limiting)
- `config/config.json` - spotdl configuration file (FLAC by default)
- `config/spotify_links.txt` - Your complete music collection (27+ albums)

## Troubleshooting

### Command not found
Make sure `~/bin` is in your PATH:
```bash
echo 'export PATH="$HOME/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

### FFmpeg not found
Install FFmpeg:
```bash
brew install ffmpeg
```

### Quotes around URLs
Always use quotes around Spotify links:
```bash
spotdl "https://open.spotify.com/track/..."
```

## Configuration

The setup automatically:
- Downloads to `~/Music/` folder
- Organizes music by Artist and Album folders
- Uses FLAC format (lossless quality) by default
- Names files with track numbers for albums
- Skips existing files to avoid duplicates
- Uses 4 threads for faster downloads
- Handles featured artists correctly (no separate folders)

Edit `~/.spotdl/config.json` to customize settings.
