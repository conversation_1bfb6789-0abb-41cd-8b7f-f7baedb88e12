#!/bin/bash
export PATH="/opt/homebrew/bin:$PATH"

echo "🐌 Slow Download Mode (Avoids Rate Limiting)"
echo "⏱️  Adds delays between downloads to prevent API errors"
echo ""

# Default file location
LINKS_FILE="$HOME/Music/spotify_links.txt"

# Check if custom file is provided
if [ "$1" ]; then
    LINKS_FILE="$1"
fi

# Check if file exists
if [ ! -f "$LINKS_FILE" ]; then
    echo "❌ File not found: $LINKS_FILE"
    exit 1
fi

echo "📁 Reading from: $LINKS_FILE"
echo ""

# Process each link with delays
while IFS= read -r line; do
    # Skip empty lines and comments
    if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
        continue
    fi
    
    # Only process Spotify links
    if [[ "$line" =~ ^https://open\.spotify\.com ]]; then
        echo "🎵 Processing: $line"
        
        # Download with config
        python3 -m spotdl --config "$line"
        
        if [ $? -eq 0 ]; then
            echo "✅ Success!"
        else
            echo "❌ Failed - continuing with next..."
        fi
        
        echo "⏱️  Waiting 10 seconds to avoid rate limiting..."
        sleep 10
        echo ""
    fi
done < "$LINKS_FILE"

echo "🎉 Slow batch processing complete!"
