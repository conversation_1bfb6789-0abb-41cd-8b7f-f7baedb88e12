#!/bin/bash

# Spotify Batch Downloader
# Optimized parallel downloader with smart duplicate detection

# Set the path for homebrew
export PATH="/opt/homebrew/bin:$PATH"

# Default settings
LINKS_FILE="$HOME/Music/spotify_links.txt"
FORCE_REDOWNLOAD=false
PARALLEL_JOBS=3  # M1 optimal: 3 albums simultaneously
MAX_THREADS=10   # Higher thread count for M1

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force|--redownload|-f)
            FORCE_REDOWNLOAD=true
            shift
            ;;
        --jobs|-j)
            PARALLEL_JOBS="$2"
            shift 2
            ;;
        --threads|-t)
            MAX_THREADS="$2"
            shift 2
            ;;
        --help|-h)
            echo "🎵 spotdl-batch - Spotify Batch Downloader"
            echo ""
            echo "Usage: spotdl-batch [OPTIONS] [FILE]"
            echo ""
            echo "Options:"
            echo "  --force, -f, --redownload    Force redownload even if files exist"
            echo "  --jobs, -j NUMBER            Parallel downloads (default: 3)"
            echo "  --threads, -t NUMBER         Threads per download (default: 10)"
            echo "  --help, -h                   Show this help message"
            echo ""
            echo "Features:"
            echo "  🎵 FLAC quality downloads"
            echo "  🔥 Parallel processing"
            echo "  🔍 Smart duplicate detection"
            echo "  📊 Detailed download statistics"
            echo ""
            echo "Examples:"
            echo "  spotdl-batch                 # Download all links"
            echo "  spotdl-batch --force         # Force redownload everything"
            echo "  spotdl-batch -j 4            # Use 4 parallel downloads"
            exit 0
            ;;
        *)
            LINKS_FILE="$1"
            shift
            ;;
    esac
done

# Check if file exists
if [ ! -f "$LINKS_FILE" ]; then
    echo "❌ File not found: $LINKS_FILE"
    echo "📝 Create the file and add Spotify links (one per line)"
    echo "💡 Usage: spotdl-batch [--force] [path/to/links.txt]"
    echo "💡 Default file: ~/Music/spotify_links.txt"
    exit 1
fi

# Check if file is empty
if [ ! -s "$LINKS_FILE" ]; then
    echo "📝 File is empty: $LINKS_FILE"
    echo "🎵 Add Spotify links to the file (one per line) and try again"
    exit 1
fi

echo "🎵 Starting parallel batch download"
echo "📁 Source: $LINKS_FILE"
echo "🎯 Destination: $HOME/Music/"
echo "⚡ Parallel jobs: $PARALLEL_JOBS"
echo "🔥 Threads per job: $MAX_THREADS"

if [ "$FORCE_REDOWNLOAD" = true ]; then
    echo "🔄 Force redownload mode: Will redownload all songs"
    SCAN_OPTION=""
    OVERWRITE_OPTION="--overwrite force"
else
    echo "🔍 Using smart duplicate detection..."
    SCAN_OPTION=""
    OVERWRITE_OPTION="--overwrite skip"
fi

echo ""

# Create temporary directory for parallel processing
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# Extract all valid Spotify links
grep "^https://open.spotify.com" "$LINKS_FILE" > "$TEMP_DIR/all_links.txt"
TOTAL_LINKS=$(wc -l < "$TEMP_DIR/all_links.txt")

echo "📊 Found $TOTAL_LINKS Spotify links to process"
echo "🎵 Starting optimized parallel downloads..."
echo ""

# Function to download a single link
download_link() {
    local link="$1"
    local job_id="$2"
    local temp_dir="$3"

    echo "🎶 [Job $job_id] Starting: $link"

    # Capture spotdl output to parse download statistics
    local output_file="$temp_dir/output_$job_id.txt"

    # Use higher thread count for M1
    python3 -m spotdl --config --threads $MAX_THREADS $SCAN_OPTION $OVERWRITE_OPTION "$link" 2>&1 | \
        tee "$output_file" | sed "s/^/[Job $job_id] /"

    local exit_code=${PIPESTATUS[0]}

    # Parse the output to count downloaded, skipped, and failed songs
    local downloaded=0
    local skipped=0
    local failed=0

    if [ -f "$output_file" ]; then
        # Count skipped songs (look for "Skipping" or "already exists")
        skipped=$(grep -c "Skipping\|already exists\|File already exists" "$output_file" 2>/dev/null || echo "0")

        # Count downloaded songs (look for "Downloaded" or success indicators, but not "Skipping")
        downloaded=$(grep -c "Downloaded\|✓\|✅" "$output_file" 2>/dev/null | grep -v "Skipping" || echo "0")

        # Count failed songs (look for "Failed" or error indicators)
        failed=$(grep -c "Failed\|❌\|Error" "$output_file" 2>/dev/null || echo "0")

        # If we can't parse individual songs, try to get totals from spotdl summary
        if [ "$downloaded" -eq 0 ] && [ "$skipped" -eq 0 ] && [ "$failed" -eq 0 ]; then
            # Look for spotdl's own summary lines
            downloaded=$(grep -o "[0-9]\+ downloaded" "$output_file" 2>/dev/null | grep -o "[0-9]\+" | head -1 || echo "0")
            skipped=$(grep -o "[0-9]\+ skipped" "$output_file" 2>/dev/null | grep -o "[0-9]\+" | head -1 || echo "0")
            failed=$(grep -o "[0-9]\+ failed" "$output_file" 2>/dev/null | grep -o "[0-9]\+" | head -1 || echo "0")
        fi

        # Ensure all values are single numbers
        downloaded=$(echo "$downloaded" | head -1 | tr -d '\n\r' | grep -o '^[0-9]\+$' || echo "0")
        skipped=$(echo "$skipped" | head -1 | tr -d '\n\r' | grep -o '^[0-9]\+$' || echo "0")
        failed=$(echo "$failed" | head -1 | tr -d '\n\r' | grep -o '^[0-9]\+$' || echo "0")
    fi

    if [ $exit_code -eq 0 ]; then
        echo "✅ [Job $job_id] Completed successfully!"
        echo "1" > "$temp_dir/success_$job_id"
    else
        echo "❌ [Job $job_id] Failed"
        echo "1" > "$temp_dir/failed_$job_id"
    fi

    # Save song counts for this job
    echo "$downloaded" > "$temp_dir/downloaded_$job_id"
    echo "$skipped" > "$temp_dir/skipped_$job_id"
    echo "$failed" > "$temp_dir/song_failed_$job_id"

    return $exit_code
}

# Export function for parallel execution
export -f download_link
export SCAN_OPTION OVERWRITE_OPTION MAX_THREADS

# Run parallel downloads using GNU parallel or xargs
if command -v parallel &> /dev/null; then
    # Use GNU parallel if available (install with: brew install parallel)
    echo "🚀 Using GNU parallel for maximum performance"
    cat "$TEMP_DIR/all_links.txt" | parallel -j $PARALLEL_JOBS --line-buffer download_link {} {#} $TEMP_DIR
else
    # Fallback to xargs with limited parallelism
    echo "⚡ Using xargs parallel processing"
    cat "$TEMP_DIR/all_links.txt" | xargs -n 1 -P $PARALLEL_JOBS -I {} bash -c 'download_link "$@"' _ {} 1 $TEMP_DIR
fi

# Calculate results
PROCESSED=$(find "$TEMP_DIR" -name "success_*" | wc -l)
FAILED=$(find "$TEMP_DIR" -name "failed_*" | wc -l)

# Calculate song statistics
TOTAL_DOWNLOADED=0
TOTAL_SKIPPED=0
TOTAL_SONG_FAILED=0

# Sum up all the song counts from individual jobs
for file in "$TEMP_DIR"/downloaded_*; do
    if [ -f "$file" ]; then
        count=$(cat "$file" 2>/dev/null | tr -d '\n\r' || echo "0")
        # Ensure count is a valid number
        if [[ "$count" =~ ^[0-9]+$ ]]; then
            TOTAL_DOWNLOADED=$((TOTAL_DOWNLOADED + count))
        fi
    fi
done

for file in "$TEMP_DIR"/skipped_*; do
    if [ -f "$file" ]; then
        count=$(cat "$file" 2>/dev/null | tr -d '\n\r' || echo "0")
        # Ensure count is a valid number
        if [[ "$count" =~ ^[0-9]+$ ]]; then
            TOTAL_SKIPPED=$((TOTAL_SKIPPED + count))
        fi
    fi
done

for file in "$TEMP_DIR"/song_failed_*; do
    if [ -f "$file" ]; then
        count=$(cat "$file" 2>/dev/null | tr -d '\n\r' || echo "0")
        # Ensure count is a valid number
        if [[ "$count" =~ ^[0-9]+$ ]]; then
            TOTAL_SONG_FAILED=$((TOTAL_SONG_FAILED + count))
        fi
    fi
done

TOTAL_SONGS=$((TOTAL_DOWNLOADED + TOTAL_SKIPPED + TOTAL_SONG_FAILED))

echo ""
echo "🎉 Batch processing complete!"
echo "📊 Summary:"
echo "   🎵 Songs downloaded: $TOTAL_DOWNLOADED"
echo "   ⏭️  Songs skipped: $TOTAL_SKIPPED"
echo "   ❌ Songs failed: $TOTAL_SONG_FAILED"
echo "   📀 Total songs processed: $TOTAL_SONGS"
echo ""
echo "   ✅ Links processed: $PROCESSED"
echo "   ❌ Links failed: $FAILED"
echo "   🚀 Used $PARALLEL_JOBS parallel jobs with $MAX_THREADS threads each"
echo "🎵 Check your Music folder: $HOME/Music/"

if [ "$FORCE_REDOWNLOAD" = true ]; then
    echo "🔄 All songs were redownloaded (force mode)"
else
    echo "💡 Note: spotdl automatically skipped any songs you already had!"
    echo "💡 Use 'spotdl-batch --force' to redownload everything"
fi

echo ""
echo "🔥 Performance Stats:"
echo "   ⚡ $((MAX_THREADS * PARALLEL_JOBS)) total concurrent threads"
echo "   🎯 Optimized for parallel processing"
