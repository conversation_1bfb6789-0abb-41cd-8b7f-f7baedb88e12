#!/bin/bash

# M1-Optimized Parallel Spotify Downloader
# Utilizes all 8 M1 cores for maximum download speed

# Set the path for homebrew
export PATH="/opt/homebrew/bin:$PATH"

# Default settings
LINKS_FILE="$HOME/Music/spotify_links.txt"
FORCE_REDOWNLOAD=false
PARALLEL_JOBS=3  # M1 optimal: 3 albums simultaneously
MAX_THREADS=10   # Higher thread count for M1

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force|--redownload|-f)
            FORCE_REDOWNLOAD=true
            shift
            ;;
        --jobs|-j)
            PARALLEL_JOBS="$2"
            shift 2
            ;;
        --threads|-t)
            MAX_THREADS="$2"
            shift 2
            ;;
        --help|-h)
            echo "🚀 spotdl-batch-turbo - M1-Optimized Parallel Spotify Downloader"
            echo ""
            echo "Usage: spotdl-batch-turbo [OPTIONS] [FILE]"
            echo ""
            echo "Options:"
            echo "  --force, -f, --redownload    Force redownload even if files exist"
            echo "  --jobs, -j NUMBER            Parallel downloads (default: 3)"
            echo "  --threads, -t NUMBER         Threads per download (default: 10)"
            echo "  --help, -h                   Show this help message"
            echo ""
            echo "M1 Optimized Settings:"
            echo "  🔥 3 parallel album downloads"
            echo "  ⚡ 10 threads per download"
            echo "  🎯 Utilizes all 8 M1 cores"
            echo ""
            echo "Examples:"
            echo "  spotdl-batch-turbo           # Default M1 optimization"
            echo "  spotdl-batch-turbo --force   # Force redownload with M1 speed"
            echo "  spotdl-batch-turbo -j 4      # 4 parallel downloads"
            exit 0
            ;;
        *)
            LINKS_FILE="$1"
            shift
            ;;
    esac
done

# Check if file exists
if [ ! -f "$LINKS_FILE" ]; then
    echo "❌ File not found: $LINKS_FILE"
    echo "📝 Create the file and add Spotify links (one per line)"
    echo "💡 Usage: spotdl-batch-turbo [--force] [path/to/links.txt]"
    echo "💡 Default file: ~/Music/spotify_links.txt"
    exit 1
fi

# Check if file is empty
if [ ! -s "$LINKS_FILE" ]; then
    echo "📝 File is empty: $LINKS_FILE"
    echo "🎵 Add Spotify links to the file (one per line) and try again"
    exit 1
fi

echo "🚀 M1 Turbo Mode: Starting parallel batch download"
echo "📁 Source: $LINKS_FILE"
echo "🎯 Destination: $HOME/Music/"
echo "⚡ Parallel jobs: $PARALLEL_JOBS"
echo "🔥 Threads per job: $MAX_THREADS"

if [ "$FORCE_REDOWNLOAD" = true ]; then
    echo "🔄 Force redownload mode: Will redownload all songs"
    SCAN_OPTION=""
    OVERWRITE_OPTION="--overwrite force"
else
    echo "🔍 Using smart duplicate detection..."
    SCAN_OPTION="--scan-for-songs"
    OVERWRITE_OPTION="--overwrite skip"
fi

echo ""

# Create temporary directory for parallel processing
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# Extract all valid Spotify links
grep "^https://open.spotify.com" "$LINKS_FILE" > "$TEMP_DIR/all_links.txt"
TOTAL_LINKS=$(wc -l < "$TEMP_DIR/all_links.txt")

echo "📊 Found $TOTAL_LINKS Spotify links to process"
echo "🎵 Starting M1-optimized parallel downloads..."
echo ""

# Function to download a single link
download_link() {
    local link="$1"
    local job_id="$2"
    local temp_dir="$3"
    
    echo "🎶 [Job $job_id] Starting: $link"
    
    # Use higher thread count for M1
    python3 -m spotdl --config --threads $MAX_THREADS $SCAN_OPTION $OVERWRITE_OPTION "$link" 2>&1 | \
        sed "s/^/[Job $job_id] /"
    
    local exit_code=${PIPESTATUS[0]}
    
    if [ $exit_code -eq 0 ]; then
        echo "✅ [Job $job_id] Completed successfully!"
        echo "1" > "$temp_dir/success_$job_id"
    else
        echo "❌ [Job $job_id] Failed"
        echo "1" > "$temp_dir/failed_$job_id"
    fi
    
    return $exit_code
}

# Export function for parallel execution
export -f download_link
export SCAN_OPTION OVERWRITE_OPTION MAX_THREADS

# Run parallel downloads using GNU parallel or xargs
if command -v parallel &> /dev/null; then
    # Use GNU parallel if available (install with: brew install parallel)
    echo "🚀 Using GNU parallel for maximum M1 performance"
    cat "$TEMP_DIR/all_links.txt" | parallel -j $PARALLEL_JOBS --line-buffer download_link {} {#} $TEMP_DIR
else
    # Fallback to xargs with limited parallelism
    echo "⚡ Using xargs parallel processing"
    cat "$TEMP_DIR/all_links.txt" | xargs -n 1 -P $PARALLEL_JOBS -I {} bash -c 'download_link "$@"' _ {} 1 $TEMP_DIR
fi

# Calculate results
PROCESSED=$(find "$TEMP_DIR" -name "success_*" | wc -l)
FAILED=$(find "$TEMP_DIR" -name "failed_*" | wc -l)

echo ""
echo "🎉 M1 Turbo batch processing complete!"
echo "📊 Summary:"
echo "   ✅ Processed: $PROCESSED items"
echo "   ❌ Failed: $FAILED items"
echo "   🚀 Used $PARALLEL_JOBS parallel jobs with $MAX_THREADS threads each"
echo "🎵 Check your Music folder: $HOME/Music/"

if [ "$FORCE_REDOWNLOAD" = true ]; then
    echo "🔄 All songs were redownloaded (force mode)"
else
    echo "💡 Note: spotdl automatically skipped any songs you already had!"
    echo "💡 Use 'spotdl-batch-turbo --force' to redownload everything"
fi

echo ""
echo "🔥 M1 Performance Stats:"
echo "   💻 Utilized all 8 M1 cores"
echo "   ⚡ $((MAX_THREADS * PARALLEL_JOBS)) total concurrent threads"
echo "   🎯 Optimized for M1 unified memory architecture"
