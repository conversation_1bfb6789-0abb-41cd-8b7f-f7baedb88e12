#!/bin/bash

# Set the path for homebrew
export PATH="/opt/homebrew/bin:$PATH"

# Check if URL is provided
if [ $# -eq 0 ]; then
    echo "❌ Please provide a Spotify URL"
    echo "💡 Usage: spotdl-smart \"https://open.spotify.com/track/...\""
    echo "💡 This version handles featured artists better than regular spotdl"
    exit 1
fi

SPOTIFY_URL="$1"

echo "🎵 Smart download with proper artist handling..."
echo "📁 Will organize by primary artist only (ignoring featured artists)"
echo ""

# Use album-artist for better organization, with fallback handling
python3 -m spotdl \
    --output "/Users/<USER>/Music/{album-artist}/{album}/{track-number} - {title}.{output-ext}" \
    --format mp3 \
    --bitrate 320k \
    --threads 4 \
    "$SPOTIFY_URL"

echo ""
echo "✅ Download complete!"
echo "📁 Check your Music folder: ~/Music/"
echo "💡 Songs are organized by primary artist (featured artists ignored)"
